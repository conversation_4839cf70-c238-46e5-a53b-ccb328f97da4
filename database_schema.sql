-- =====================================================
-- TALOS PostgreSQL Database Schema
-- Designed to replace JSON-based data storage
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CORE REFERENCE TABLES
-- =====================================================

-- Technologies (AN800, AN350, AN200)
CREATE TABLE technologies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Corridors (Prototype, Engineering, Production, etc.)
CREATE TABLE corridors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users/Requesters
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    asana_gid VARCHAR(255) UNIQUE,
    acronym VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Task Types (PI, Tape-Out, RFQ-PO, Testing, etc.)
CREATE TABLE task_types (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    sequence_order INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- CAPACITY AND CONFIGURATION
-- =====================================================

-- Capacity Configuration
CREATE TABLE capacity_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    corridor_id UUID REFERENCES corridors(id),
    technology_id UUID REFERENCES technologies(id),
    max_lots INTEGER NOT NULL,
    max_wafers INTEGER NOT NULL,
    production_days VARCHAR(255), -- "Monday,Tuesday,Wednesday"
    task_delay_max_days INTEGER DEFAULT 0,
    task_advance_max_days INTEGER DEFAULT 0,
    max_reservation_days INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(corridor_id, technology_id)
);

-- Module Categories (Operational, Engineering)
CREATE TABLE module_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Modules
CREATE TABLE modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    technology_id UUID REFERENCES technologies(id),
    category_id UUID REFERENCES module_categories(id),
    platform VARCHAR(100),
    production_time_days INTEGER DEFAULT 0,
    rerun_time_days INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- PROJECT MANAGEMENT
-- =====================================================

-- Projects (Main entity)
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    asana_gid VARCHAR(255) UNIQUE,
    technology_id UUID REFERENCES technologies(id),
    corridor_id UUID REFERENCES corridors(id),
    requester_id UUID REFERENCES users(id),
    status VARCHAR(50) DEFAULT 'Active',
    start_date DATE,
    due_date DATE,
    cycle_time_days INTEGER,
    lot_project VARCHAR(255),
    mask_design_name VARCHAR(255),
    wafer_number INTEGER,
    modules TEXT, -- JSON or comma-separated list
    priority VARCHAR(20) DEFAULT 'Medium', -- Low, Medium, High
    sales_order VARCHAR(100),
    customer_id VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Project Modules (Many-to-Many relationship)
CREATE TABLE project_modules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    module_id UUID REFERENCES modules(id),
    is_selected BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, module_id)
);

-- Tasks
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    task_type_id UUID REFERENCES task_types(id),
    name VARCHAR(255) NOT NULL,
    asana_gid VARCHAR(255) UNIQUE,
    start_date DATE,
    due_date DATE,
    status VARCHAR(50) DEFAULT 'Not Started',
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    assignee_gid VARCHAR(255),
    notes TEXT,
    custom_fields JSONB, -- Store Asana custom fields
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LOT AND MASK MANAGEMENT
-- =====================================================

-- Masks
CREATE TABLE masks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    mask_code VARCHAR(10) NOT NULL UNIQUE, -- AAA, AAB, etc.
    project_name VARCHAR(255),
    technology_id UUID REFERENCES technologies(id),
    run_number INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'Active',
    created_date DATE DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Lots
CREATE TABLE lots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lot_number VARCHAR(50) NOT NULL UNIQUE,
    project_id UUID REFERENCES projects(id),
    technology_id UUID REFERENCES technologies(id),
    corridor_id UUID REFERENCES corridors(id),
    mask_code VARCHAR(10),
    run_number INTEGER DEFAULT 1,
    layer_code VARCHAR(10) DEFAULT 'M00',
    wafer_count INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'Reserved', -- Reserved, Confirmed, In_Progress, Completed
    reservation_type VARCHAR(50),
    reserved_date DATE DEFAULT CURRENT_DATE,
    confirmed_date DATE,
    requester_id UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ASANA INTEGRATION
-- =====================================================

-- Asana Projects Configuration
CREATE TABLE asana_projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    asana_gid VARCHAR(255) NOT NULL UNIQUE,
    portfolio_name VARCHAR(255),
    portfolio_gid VARCHAR(255),
    project_type VARCHAR(100), -- PI, Testing, DataProcessing, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Email Configuration
CREATE TABLE email_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    email_address VARCHAR(255) NOT NULL,
    receive_alerts BOOLEAN DEFAULT true,
    receive_updates BOOLEAN DEFAULT true,
    notification_preferences JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, email_address)
);

-- =====================================================
-- PERFORMANCE AND CACHING
-- =====================================================

-- Project Cache for performance
CREATE TABLE project_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    cache_type VARCHAR(100) NOT NULL, -- 'project_data', 'task_summary', etc.
    cached_data JSONB NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, cache_type)
);

-- =====================================================
-- AUDIT AND LOGGING
-- =====================================================

-- Audit logs for tracking changes
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    record_id UUID NOT NULL,
    user_id UUID REFERENCES users(id),
    old_values JSONB,
    new_values JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Project indexes
CREATE INDEX idx_projects_technology ON projects(technology_id);
CREATE INDEX idx_projects_corridor ON projects(corridor_id);
CREATE INDEX idx_projects_requester ON projects(requester_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_dates ON projects(start_date, due_date);
CREATE INDEX idx_projects_asana_gid ON projects(asana_gid);

-- Task indexes
CREATE INDEX idx_tasks_project ON tasks(project_id);
CREATE INDEX idx_tasks_type ON tasks(task_type_id);
CREATE INDEX idx_tasks_status ON tasks(status, completed);
CREATE INDEX idx_tasks_dates ON tasks(start_date, due_date);
CREATE INDEX idx_tasks_asana_gid ON tasks(asana_gid);

-- Lot indexes
CREATE INDEX idx_lots_project ON lots(project_id);
CREATE INDEX idx_lots_technology ON lots(technology_id);
CREATE INDEX idx_lots_corridor ON lots(corridor_id);
CREATE INDEX idx_lots_status ON lots(status);
CREATE INDEX idx_lots_requester ON lots(requester_id);

-- Cache indexes
CREATE INDEX idx_cache_project_type ON project_cache(project_id, cache_type);
CREATE INDEX idx_cache_expires ON project_cache(expires_at);

-- Audit indexes
CREATE INDEX idx_audit_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_user ON audit_logs(user_id);
CREATE INDEX idx_audit_created ON audit_logs(created_at);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_technologies_updated_at BEFORE UPDATE ON technologies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_corridors_updated_at BEFORE UPDATE ON corridors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_capacity_config_updated_at BEFORE UPDATE ON capacity_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_modules_updated_at BEFORE UPDATE ON modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_masks_updated_at BEFORE UPDATE ON masks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_lots_updated_at BEFORE UPDATE ON lots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_asana_projects_updated_at BEFORE UPDATE ON asana_projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_configs_updated_at BEFORE UPDATE ON email_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_cache_updated_at BEFORE UPDATE ON project_cache FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
