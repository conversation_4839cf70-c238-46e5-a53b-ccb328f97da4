from django.db import models
import uuid
from django.contrib.auth.models import User
from django.utils import timezone


class BaseModel(models.Model):
    """Abstract base model with common fields"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


class Technology(BaseModel):
    """Technology platforms (AN800, AN350, AN200)"""
    name = models.CharField(max_length=50, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Technologies"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Corridor(BaseModel):
    """Corridors (Prototype, Engineering, Production, etc.)"""
    name = models.Char<PERSON>ield(max_length=50, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class TalosUser(BaseModel):
    """Users/Requesters in the system"""
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True, null=True, blank=True)
    asana_gid = models.CharField(max_length=255, unique=True, null=True, blank=True)
    acronym = models.CharField(max_length=20, blank=True)

    class Meta:
        verbose_name = "Talos User"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.acronym})" if self.acronym else self.name


class TaskType(models.Model):
    """Types of tasks (PI, Tape-Out, RFQ-PO, etc.)"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    sequence_order = models.IntegerField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['sequence_order', 'name']

    def __str__(self):
        return self.name


class CapacityConfig(BaseModel):
    """Capacity configuration per technology and corridor"""
    corridor = models.ForeignKey(Corridor, on_delete=models.CASCADE)
    technology = models.ForeignKey(Technology, on_delete=models.CASCADE)
    max_lots = models.IntegerField()
    max_wafers = models.IntegerField()
    production_days = models.CharField(max_length=255, help_text="Comma-separated days: Monday,Tuesday,etc")
    task_delay_max_days = models.IntegerField(default=0)
    task_advance_max_days = models.IntegerField(default=0)
    max_reservation_days = models.IntegerField(default=30)

    class Meta:
        unique_together = ['corridor', 'technology']
        verbose_name = "Capacity Configuration"

    def __str__(self):
        return f"{self.corridor.name} - {self.technology.name}"


class ModuleCategory(BaseModel):
    """Module categories (Operational, Engineering)"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Module Categories"
        ordering = ['name']

    def __str__(self):
        return self.name


class Module(BaseModel):
    """Process modules"""
    name = models.CharField(max_length=255)
    display_name = models.CharField(max_length=255)
    technology = models.ForeignKey(Technology, on_delete=models.CASCADE)
    category = models.ForeignKey(ModuleCategory, on_delete=models.CASCADE)
    platform = models.CharField(max_length=100, blank=True)
    production_time_days = models.IntegerField(default=0)
    rerun_time_days = models.IntegerField(default=0)

    class Meta:
        ordering = ['technology', 'category', 'name']

    def __str__(self):
        return f"{self.display_name} ({self.technology.name})"


class Project(BaseModel):
    """Main projects table"""
    name = models.CharField(max_length=255)
    asana_gid = models.CharField(max_length=255, unique=True, null=True, blank=True)
    technology = models.ForeignKey(Technology, on_delete=models.CASCADE)
    corridor = models.ForeignKey(Corridor, on_delete=models.CASCADE)
    requester = models.ForeignKey(TalosUser, on_delete=models.CASCADE)

    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Completed', 'Completed'),
        ('On Hold', 'On Hold'),
        ('Cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Active')

    start_date = models.DateField(null=True, blank=True)
    due_date = models.DateField(null=True, blank=True)
    cycle_time_days = models.IntegerField(null=True, blank=True)
    lot_project = models.CharField(max_length=255, blank=True)
    mask_design_name = models.CharField(max_length=255, blank=True)
    wafer_number = models.IntegerField(null=True, blank=True)
    modules = models.TextField(blank=True, help_text="JSON or comma-separated list")

    PRIORITY_CHOICES = [
        ('Low', 'Low'),
        ('Medium', 'Medium'),
        ('High', 'High'),
    ]
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='Medium')

    sales_order = models.CharField(max_length=100, blank=True)
    customer_id = models.CharField(max_length=100, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    @property
    def duration_days(self):
        """Calculate project duration in days"""
        if self.start_date and self.due_date:
            return (self.due_date - self.start_date).days
        return None


class ProjectModule(models.Model):
    """Many-to-many relationship between projects and modules"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='project_modules')
    module = models.ForeignKey(Module, on_delete=models.CASCADE)
    is_selected = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['project', 'module']

    def __str__(self):
        return f"{self.project.name} - {self.module.display_name}"


class Task(BaseModel):
    """Individual tasks within projects"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='tasks')
    task_type = models.ForeignKey(TaskType, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    asana_gid = models.CharField(max_length=255, unique=True, null=True, blank=True)

    start_date = models.DateField(null=True, blank=True)
    due_date = models.DateField(null=True, blank=True)

    STATUS_CHOICES = [
        ('Not Started', 'Not Started'),
        ('In Progress', 'In Progress'),
        ('Completed', 'Completed'),
        ('On Hold', 'On Hold'),
        ('Cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Not Started')
    completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    assignee_gid = models.CharField(max_length=255, blank=True)
    notes = models.TextField(blank=True)
    custom_fields = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['project', 'task_type__sequence_order']

    def __str__(self):
        return f"{self.project.name} - {self.task_type.name}"


class Mask(BaseModel):
    """Mask designs used in manufacturing"""
    mask_code = models.CharField(max_length=10, unique=True)
    project_name = models.CharField(max_length=255, blank=True)
    technology = models.ForeignKey(Technology, on_delete=models.CASCADE)
    run_number = models.IntegerField(default=1)

    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Completed', 'Completed'),
        ('Retired', 'Retired'),
    ]
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Active')
    created_date = models.DateField(auto_now_add=True)

    class Meta:
        ordering = ['mask_code']

    def __str__(self):
        return f"{self.mask_code} ({self.technology.name})"


class Lot(BaseModel):
    """Manufacturing lots associated with projects"""
    lot_number = models.CharField(max_length=50, unique=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='lots')
    technology = models.ForeignKey(Technology, on_delete=models.CASCADE)
    corridor = models.ForeignKey(Corridor, on_delete=models.CASCADE)
    mask_code = models.CharField(max_length=10, blank=True)
    run_number = models.IntegerField(default=1)
    layer_code = models.CharField(max_length=10, default='M00')
    wafer_count = models.IntegerField()

    STATUS_CHOICES = [
        ('Reserved', 'Reserved'),
        ('Confirmed', 'Confirmed'),
        ('In_Progress', 'In Progress'),
        ('Completed', 'Completed'),
        ('Cancelled', 'Cancelled'),
    ]
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Reserved')
    reservation_type = models.CharField(max_length=50, blank=True)
    reserved_date = models.DateField(auto_now_add=True)
    confirmed_date = models.DateField(null=True, blank=True)
    requester = models.ForeignKey(TalosUser, on_delete=models.CASCADE)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.lot_number} ({self.project.name})"


class AsanaProject(BaseModel):
    """Asana projects configuration"""
    name = models.CharField(max_length=255)
    asana_gid = models.CharField(max_length=255, unique=True)
    portfolio_name = models.CharField(max_length=255, blank=True)
    portfolio_gid = models.CharField(max_length=255, blank=True)
    project_type = models.CharField(max_length=100, blank=True)

    class Meta:
        verbose_name = "Asana Project"
        ordering = ['name']

    def __str__(self):
        return self.name


class EmailConfig(BaseModel):
    """Email configuration for users"""
    user = models.ForeignKey(TalosUser, on_delete=models.CASCADE, related_name='email_configs')
    email_address = models.EmailField()
    receive_alerts = models.BooleanField(default=True)
    receive_updates = models.BooleanField(default=True)
    notification_preferences = models.JSONField(default=dict, blank=True)

    class Meta:
        unique_together = ['user', 'email_address']
        verbose_name = "Email Configuration"

    def __str__(self):
        return f"{self.user.name} - {self.email_address}"


class ProjectCache(models.Model):
    """Performance cache for frequently accessed project data"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='cache_entries')
    cache_type = models.CharField(max_length=100)
    cached_data = models.JSONField()
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['project', 'cache_type']
        verbose_name = "Project Cache"

    def __str__(self):
        return f"{self.project.name} - {self.cache_type}"

    @property
    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at


class AuditLog(models.Model):
    """Audit trail for all data changes"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    table_name = models.CharField(max_length=100)

    OPERATION_CHOICES = [
        ('INSERT', 'Insert'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete'),
    ]
    operation = models.CharField(max_length=20, choices=OPERATION_CHOICES)
    record_id = models.UUIDField()
    user = models.ForeignKey(TalosUser, on_delete=models.SET_NULL, null=True, blank=True)
    old_values = models.JSONField(null=True, blank=True)
    new_values = models.JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Audit Log"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.operation} on {self.table_name} at {self.created_at}"
