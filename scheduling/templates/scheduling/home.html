﻿{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Talos Scheduling System - Professional wafer fabrication scheduling and management platform">
    <meta name="keywords" content="Talos, scheduling, wafer, fabrication, semiconductor, manufacturing">
    <meta name="author" content="Ligentec">
    <title>Talos - Authentication | Scheduling System</title>

    <!-- Favicon -->
    <link rel="icon" type="image/gif" href="{% static 'scheduling/logo.gif' %}">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- SweetAlert2 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'talos-primary': '#4f46e5',
                        'talos-secondary': '#7c3aed',
                        'talos-accent': '#06b6d4',
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>

<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full floating-animation"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white opacity-5 rounded-full floating-animation" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/4 w-32 h-32 bg-white opacity-10 rounded-full pulse-animation"></div>
    </div>

    <!-- Main Login Container -->
    <div class="relative z-10 w-full max-w-md">
        <!-- Logo and Title Section -->
        <div class="text-center mb-8">
            <div class="mb-6">
                <img src="{% static 'scheduling/logo.gif' %}" alt="Talos Logo" class="mx-auto h-20 w-auto drop-shadow-lg">
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">Welcome to Talos</h1>
            <p class="text-white/80 text-lg">Please sign in to continue</p>
        </div>

        <!-- Login Card -->
        <div class="login-card rounded-2xl p-8 shadow-2xl">
            <form method="post" id="loginForm" class="space-y-6">
                {% csrf_token %}

                <!-- Username Field -->
                <div class="space-y-2">
                    <label for="id_requester" class="block text-sm font-semibold text-gray-700">
                        Username
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        {{ form.requester }}
                    </div>
                </div>

                <!-- Password Field -->
                <div class="space-y-2">
                    <label for="id_password" class="block text-sm font-semibold text-gray-700">
                        Password <span class="text-gray-500 text-xs">(Optional)</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        {{ form.password }}
                    </div>
                </div>

                <!-- Login Button -->
                <div class="pt-4">
                    <button type="submit" id="loginButton" class="w-full bg-gradient-to-r from-talos-primary to-talos-secondary hover:from-talos-secondary hover:to-talos-primary text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-4 focus:ring-talos-primary/50">
                        <span id="buttonText">Sign In</span>
                        <svg id="loadingSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white inline" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- Help Text -->
        <div class="text-center mt-6">
            <div class="glass-effect rounded-lg p-4 text-white/90 text-sm">
                <p class="mb-2"><strong>Quick Tips:</strong></p>
                <p>• Select your username from the dropdown</p>
                <p>• Password is optional for most users</p>
                <p>• Press <kbd class="bg-white/20 px-2 py-1 rounded text-xs">Enter</kbd> to sign in</p>
                <p>• Press <kbd class="bg-white/20 px-2 py-1 rounded text-xs">Esc</kbd> to clear form</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-white/60 text-sm">© 2024 Talos Scheduling System</p>
            <p class="text-white/40 text-xs mt-1">Professional Wafer Fabrication Management</p>
        </div>
    </div>

    <script>
        // Enhanced form handling with SweetAlert
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = this;
            const button = document.getElementById('loginButton');
            const buttonText = document.getElementById('buttonText');
            const spinner = document.getElementById('loadingSpinner');
            const requesterField = document.getElementById('id_requester');

            // Validate username selection
            if (!requesterField.value) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Username Required',
                    text: 'Please select a username to continue.',
                    confirmButtonColor: '#4f46e5',
                    background: '#ffffff',
                    backdrop: 'rgba(0,0,0,0.4)'
                });
                return;
            }

            // Show loading state
            button.disabled = true;
            buttonText.textContent = 'Signing In...';
            spinner.classList.remove('hidden');

            // Submit form after a brief delay for UX
            setTimeout(() => {
                form.submit();
            }, 500);
        });

        // Style the form fields with Tailwind classes and add enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Style the username dropdown
            const requesterField = document.getElementById('id_requester');
            if (requesterField) {
                requesterField.className = 'w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-talos-primary focus:border-transparent transition-all duration-200 bg-white/90';

                // Auto-focus on the username field
                requesterField.focus();

                // Add change event to enable password field when username is selected
                requesterField.addEventListener('change', function() {
                    const passwordField = document.getElementById('id_password');
                    if (this.value && passwordField) {
                        passwordField.removeAttribute('disabled');
                        passwordField.placeholder = 'Enter password (optional)';
                    }
                });
            }

            // Style the password field
            const passwordField = document.getElementById('id_password');
            if (passwordField) {
                passwordField.className = 'w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-talos-primary focus:border-transparent transition-all duration-200 bg-white/90';
                passwordField.type = 'password';
                passwordField.placeholder = 'Enter password (optional)';

                // Initially disable password field if no username selected
                if (!requesterField.value) {
                    passwordField.setAttribute('disabled', 'disabled');
                    passwordField.placeholder = 'Select username first';
                }
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Enter key submits form
                if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey) {
                    const form = document.getElementById('loginForm');
                    if (form && requesterField.value) {
                        e.preventDefault();
                        form.dispatchEvent(new Event('submit'));
                    }
                }

                // Escape key clears form
                if (e.key === 'Escape') {
                    requesterField.selectedIndex = 0;
                    passwordField.value = '';
                    passwordField.setAttribute('disabled', 'disabled');
                    passwordField.placeholder = 'Select username first';
                    requesterField.focus();
                }
            });
        });

        // Show error messages with SweetAlert if there are form errors
        {% if form.errors %}
            document.addEventListener('DOMContentLoaded', function() {
                let errorMessages = [];
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        errorMessages.push('{{ error|escapejs }}');
                    {% endfor %}
                {% endfor %}

                Swal.fire({
                    icon: 'error',
                    title: 'Authentication Error',
                    html: errorMessages.length > 0 ? errorMessages.join('<br>') : 'Please check your credentials and try again.',
                    confirmButtonColor: '#4f46e5',
                    background: '#ffffff',
                    backdrop: 'rgba(0,0,0,0.4)'
                });
            });
        {% endif %}

        {% if error_message %}
            document.addEventListener('DOMContentLoaded', function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Authentication Error',
                    text: '{{ error_message|escapejs }}',
                    confirmButtonColor: '#4f46e5',
                    background: '#ffffff',
                    backdrop: 'rgba(0,0,0,0.4)'
                });
            });
        {% endif %}
    </script>
</body>
</html>

