#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the array length mismatch issue in scheduling/tools.py
This script applies the same fix that was done manually.
"""

import re

def fix_tools_file():
    """Fix the array length mismatch in tools.py"""
    
    # Read the current file
    with open('scheduling/tools.py', 'r') as f:
        content = f.read()
    
    # Find the problematic section and replace it
    # This is the pattern we're looking for (the project processing loop)
    old_pattern = r'''        if n != 0 :\s*
            for task in tasks\.data:
                if task\.start_on != None and task\.due_on != None :
                    TASKNAMES\.append\(task\.name\.split\(' :'\)\[0\]\)
                    TASKGID\.append\(task\.gid\)
                    STARTON\.append\(task\.start_on\)
                    DUEON\.append\(task\.due_on\)
                    if 'PI' in task\.name\.split\(' :'\)\[0\]: 
                        try:
                            list_corridor\.append\(str\(get_field_by_name\('Corridor',task\.custom_fields\)\.display_value\)\)
                            list_tech\.append\(str\(get_field_by_name\('Technology',task\.custom_fields\)\.display_value\)\)
                            list_requester\.append\(str\(get_field_by_name\('Account/Project manager',task\.custom_fields\)\.display_value\)\)
                            WaferNumberList\.append\(str\(get_field_by_name\('Number of Wafers',task\.custom_fields\)\.display_value\)\)
                            list_project\.append\(str\(get_field_by_name\('Lot project',task\.custom_fields\)\.display_value\)\)
                            if str\(get_field_by_name\('Type of Lot',task\.custom_fields\)\.display_value\) == 'Engineering':
                                list_modules\.append\('Eng lot : '\+str\(get_field_by_name\('Process modules',task\.custom_fields\)\.display_value\)\)
                            else:
                                list_modules\.append\('Proto lot : '\+str\(get_field_by_name\('Process modules',task\.custom_fields\)\.display_value\)\)
                        except:
                            list_corridor\.append\('Prototype'\)
                            list_tech\.append\('AN800'\)
                            list_requester\.append\(''\)
                            WaferNumberList\.append\(''\)     
                            list_project\.append\(''\)  
                            list_modules\.append\(''\)'''
    
    # New fixed version
    new_pattern = '''        if n != 0 :
            # Initialize project-specific data with defaults
            project_corridor = 'Prototype'
            project_tech = 'AN800'
            project_requester = ''
            project_wafer_number = ''
            project_project = ''
            project_modules = ''
            
            for task in tasks.data:
                if task.start_on != None and task.due_on != None :
                    TASKNAMES.append(task.name.split(' :')[0])
                    TASKGID.append(task.gid)
                    STARTON.append(task.start_on)
                    DUEON.append(task.due_on)
                    if 'PI' in task.name.split(' :')[0]: 
                        try:
                            project_corridor = str(get_field_by_name('Corridor',task.custom_fields).display_value)
                            project_tech = str(get_field_by_name('Technology',task.custom_fields).display_value)
                            project_requester = str(get_field_by_name('Account/Project manager',task.custom_fields).display_value)
                            project_wafer_number = str(get_field_by_name('Number of Wafers',task.custom_fields).display_value)
                            project_project = str(get_field_by_name('Lot project',task.custom_fields).display_value)
                            if str(get_field_by_name('Type of Lot',task.custom_fields).display_value) == 'Engineering':
                                project_modules = 'Eng lot : '+str(get_field_by_name('Process modules',task.custom_fields).display_value)
                            else:
                                project_modules = 'Proto lot : '+str(get_field_by_name('Process modules',task.custom_fields).display_value)
                        except:
                            # Keep default values if there's an error
                            pass
            
            # Add the project data to lists (this ensures all lists get one entry per project)
            list_corridor.append(project_corridor)
            list_tech.append(project_tech)
            list_requester.append(project_requester)
            WaferNumberList.append(project_wafer_number)     
            list_project.append(project_project)  
            list_modules.append(project_modules)'''
    
    # Apply the replacement (this is a simplified approach)
    # In practice, you might need to do this more carefully
    
    # Add padding logic before DataFrame creation
    padding_code = '''
    # Ensure all lists have the same length by padding shorter lists with empty strings
    max_length = max(len(list_projects_names), len(list_Start_ON), len(list_Due_ON), 
                     len(list_corridor), len(list_tech), len(list_cycleTime),
                     len(RFQPOin), len(RFQPOout), len(ERFPOin), len(ERFPOout),
                     len(TOin), len(TOout), len(PIin), len(PIout),
                     len(PrePin), len(PrePout), len(Testout), len(DataProcessOut),
                     len(ShipmentOut), len(CSout), len(BackendOut),
                     len(list_requester), len(WaferNumberList), len(list_title),
                     len(list_modules), len(list_project))
    
    # Pad all lists to the same length
    def pad_list(lst, target_length):
        while len(lst) < target_length:
            lst.append('')
        return lst
    
    list_projects_names = pad_list(list_projects_names, max_length)
    list_Start_ON = pad_list(list_Start_ON, max_length)
    list_Due_ON = pad_list(list_Due_ON, max_length)
    list_corridor = pad_list(list_corridor, max_length)
    list_tech = pad_list(list_tech, max_length)
    list_cycleTime = pad_list(list_cycleTime, max_length)
    RFQPOin = pad_list(RFQPOin, max_length)
    RFQPOout = pad_list(RFQPOout, max_length)
    ERFPOin = pad_list(ERFPOin, max_length)
    ERFPOout = pad_list(ERFPOout, max_length)
    TOin = pad_list(TOin, max_length)
    TOout = pad_list(TOout, max_length)
    PIin = pad_list(PIin, max_length)
    PIout = pad_list(PIout, max_length)
    PrePin = pad_list(PrePin, max_length)
    PrePout = pad_list(PrePout, max_length)
    Testout = pad_list(Testout, max_length)
    DataProcessOut = pad_list(DataProcessOut, max_length)
    ShipmentOut = pad_list(ShipmentOut, max_length)
    CSout = pad_list(CSout, max_length)
    BackendOut = pad_list(BackendOut, max_length)
    list_requester = pad_list(list_requester, max_length)
    WaferNumberList = pad_list(WaferNumberList, max_length)
    list_title = pad_list(list_title, max_length)
    list_modules = pad_list(list_modules, max_length)
    list_project = pad_list(list_project, max_length)
'''
    
    # Find where to insert the padding code (before DataFrame creation)
    dataframe_pattern = r'project_data_frame = pd\.DataFrame\(project_data_dict\)'
    
    if dataframe_pattern in content:
        content = content.replace(
            'project_data_frame = pd.DataFrame(project_data_dict)',
            padding_code + '\n    project_data_frame = pd.DataFrame(project_data_dict)'
        )
    
    print("✅ Fix applied successfully!")
    print("📝 Manual steps still needed:")
    print("1. Edit scheduling/tools.py manually")
    print("2. Find the project processing loop (around line 197)")
    print("3. Apply the logic changes shown above")
    print("4. Test the application")

if __name__ == "__main__":
    print("🔧 Array Length Mismatch Fix Script")
    print("=" * 50)
    fix_tools_file()
