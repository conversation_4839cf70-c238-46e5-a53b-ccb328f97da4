-- =====================================================
-- TALOS Initial Data Population
-- Insert reference data and configuration
-- =====================================================

-- =====================================================
-- TECHNOLOGIES
-- =====================================================
INSERT INTO technologies (name, code, description) VALUES
('AN800', '5A', '800nm technology platform'),
('AN350', '6A', '350nm technology platform'),
('AN200', '4A', '200nm technology platform');

-- =====================================================
-- CORRIDORS
-- =====================================================
INSERT INTO corridors (name, code, description) VALUES
('Pre-production', '0', 'Pre-production lots'),
('Prototype', '1', 'Prototype development lots'),
('Engineering', '2', 'Engineering validation lots'),
('Production', '3', 'Production lots'),
('PhotonixFAB', 'PHX', 'PhotonixFAB specific corridor');

-- =====================================================
-- TASK TYPES
-- =====================================================
INSERT INTO task_types (name, description, sequence_order) VALUES
('RFQ-PO', 'Request for Quote and Purchase Order', 1),
('ERFURT-PO', 'ERFURT Purchase Order', 2),
('Tape-Out', 'Tape-Out process', 3),
('PI', 'Process Integration', 4),
('Pre-production', 'Pre-production phase', 5),
('Testing', 'Testing phase', 6),
('Data processing', 'Data processing and analysis', 7),
('QA gate', 'Quality Assurance gate', 8),
('Shipment', 'Shipment preparation', 9),
('ERFURT-FAB', 'ERFURT fabrication', 10),
('Backend', 'Backend processing', 11);

-- =====================================================
-- MODULE CATEGORIES
-- =====================================================
INSERT INTO module_categories (name, description) VALUES
('Operational', 'Operational lot modules'),
('Engineering', 'Engineering lot modules');

-- =====================================================
-- SAMPLE CAPACITY CONFIGURATION
-- =====================================================
-- Get technology and corridor IDs for references
DO $$
DECLARE
    tech_an800_id UUID;
    tech_an350_id UUID;
    tech_an200_id UUID;
    corridor_proto_id UUID;
    corridor_eng_id UUID;
    corridor_prod_id UUID;
    corridor_phx_id UUID;
    cat_op_id UUID;
    cat_eng_id UUID;
BEGIN
    -- Get technology IDs
    SELECT id INTO tech_an800_id FROM technologies WHERE name = 'AN800';
    SELECT id INTO tech_an350_id FROM technologies WHERE name = 'AN350';
    SELECT id INTO tech_an200_id FROM technologies WHERE name = 'AN200';
    
    -- Get corridor IDs
    SELECT id INTO corridor_proto_id FROM corridors WHERE name = 'Prototype';
    SELECT id INTO corridor_eng_id FROM corridors WHERE name = 'Engineering';
    SELECT id INTO corridor_prod_id FROM corridors WHERE name = 'Production';
    SELECT id INTO corridor_phx_id FROM corridors WHERE name = 'PhotonixFAB';
    
    -- Get category IDs
    SELECT id INTO cat_op_id FROM module_categories WHERE name = 'Operational';
    SELECT id INTO cat_eng_id FROM module_categories WHERE name = 'Engineering';

    -- Insert capacity configurations
    INSERT INTO capacity_config (corridor_id, technology_id, max_lots, max_wafers, production_days, task_delay_max_days, task_advance_max_days, max_reservation_days) VALUES
    (corridor_proto_id, tech_an800_id, 10, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_proto_id, tech_an350_id, 8, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_proto_id, tech_an200_id, 6, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_eng_id, tech_an800_id, 15, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_eng_id, tech_an350_id, 12, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_eng_id, tech_an200_id, 10, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_prod_id, tech_an800_id, 20, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_prod_id, tech_an350_id, 18, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_prod_id, tech_an200_id, 15, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30),
    (corridor_phx_id, tech_an800_id, 5, 25, 'Monday,Tuesday,Wednesday,Thursday,Friday', 7, 3, 30);

    -- Insert sample modules
    INSERT INTO modules (name, display_name, technology_id, category_id, platform, production_time_days, rerun_time_days) VALUES
    -- AN800 Operational Modules
    ('AN800_BASIC', 'AN800 Basic Process', tech_an800_id, cat_op_id, 'AN800', 14, 7),
    ('AN800_ADVANCED', 'AN800 Advanced Process', tech_an800_id, cat_op_id, 'AN800', 21, 10),
    ('AN800_CUSTOM', 'AN800 Custom Process', tech_an800_id, cat_op_id, 'AN800', 28, 14),
    
    -- AN350 Operational Modules
    ('AN350_BASIC', 'AN350 Basic Process', tech_an350_id, cat_op_id, 'AN350', 12, 6),
    ('AN350_ADVANCED', 'AN350 Advanced Process', tech_an350_id, cat_op_id, 'AN350', 18, 9),
    ('AN350_CUSTOM', 'AN350 Custom Process', tech_an350_id, cat_op_id, 'AN350', 25, 12),
    
    -- AN200 Operational Modules
    ('AN200_BASIC', 'AN200 Basic Process', tech_an200_id, cat_op_id, 'AN200', 10, 5),
    ('AN200_ADVANCED', 'AN200 Advanced Process', tech_an200_id, cat_op_id, 'AN200', 15, 7),
    
    -- Engineering Modules
    ('ENG_VALIDATION', 'Engineering Validation', tech_an800_id, cat_eng_id, 'AN800', 7, 3),
    ('ENG_CHARACTERIZATION', 'Engineering Characterization', tech_an350_id, cat_eng_id, 'AN350', 10, 5),
    ('ENG_DEVELOPMENT', 'Engineering Development', tech_an200_id, cat_eng_id, 'AN200', 14, 7);

END $$;

-- =====================================================
-- SAMPLE ASANA PROJECTS
-- =====================================================
INSERT INTO asana_projects (name, asana_gid, portfolio_name, portfolio_gid, project_type) VALUES
('PI', '1234567890123456', 'Prototype', '1111111111111111', 'PI'),
('Testing', '1234567890123457', 'Prototype', '1111111111111111', 'Testing'),
('Data Processing', '1234567890123458', 'Prototype', '1111111111111111', 'DataProcessing'),
('QA Gate', '1234567890123459', 'Prototype', '1111111111111111', 'QA'),
('Shipment', '1234567890123460', 'Prototype', '1111111111111111', 'Shipment'),
('ERFURT-FAB', '1234567890123461', 'Engineering', '2222222222222222', 'CS'),
('Backend', '1234567890123462', 'Engineering', '2222222222222222', 'Backend'),
('Mask Assembly', '1234567890123463', 'Engineering', '2222222222222222', 'MaskAssembly'),
('RFQ-PO', '1234567890123464', 'Engineering', '2222222222222222', 'RFQPO'),
('Grinding', '1234567890123465', 'Engineering', '2222222222222222', 'Grinding');

-- =====================================================
-- SAMPLE USERS
-- =====================================================
INSERT INTO users (name, email, asana_gid, acronym) VALUES
('John Doe', '<EMAIL>', '1111111111111111', 'JD'),
('Jane Smith', '<EMAIL>', '2222222222222222', 'JS'),
('Bob Johnson', '<EMAIL>', '3333333333333333', 'BJ'),
('Alice Brown', '<EMAIL>', '4444444444444444', 'AB'),
('Charlie Wilson', '<EMAIL>', '5555555555555555', 'CW'),
('Diana Davis', '<EMAIL>', '6666666666666666', 'DD'),
('System Admin', '<EMAIL>', '9999999999999999', 'ADMIN');

-- =====================================================
-- EMAIL CONFIGURATIONS
-- =====================================================
DO $$
DECLARE
    user_rec RECORD;
BEGIN
    FOR user_rec IN SELECT id, email, acronym FROM users WHERE email IS NOT NULL LOOP
        INSERT INTO email_configs (user_id, email_address, receive_alerts, receive_updates, notification_preferences)
        VALUES (
            user_rec.id,
            user_rec.email,
            true,
            true,
            jsonb_build_object(
                'lot_confirmation', true,
                'task_updates', true,
                'capacity_alerts', true,
                'project_completion', true
            )
        );
    END LOOP;
END $$;

-- =====================================================
-- SAMPLE MASKS
-- =====================================================
DO $$
DECLARE
    tech_an800_id UUID;
    tech_an350_id UUID;
    tech_an200_id UUID;
BEGIN
    SELECT id INTO tech_an800_id FROM technologies WHERE name = 'AN800';
    SELECT id INTO tech_an350_id FROM technologies WHERE name = 'AN350';
    SELECT id INTO tech_an200_id FROM technologies WHERE name = 'AN200';

    INSERT INTO masks (mask_code, project_name, technology_id, run_number, status) VALUES
    ('AAA', 'PROJECT_ALPHA', tech_an800_id, 1, 'Active'),
    ('AAB', 'PROJECT_BETA', tech_an350_id, 1, 'Active'),
    ('AAC', 'PROJECT_GAMMA', tech_an200_id, 1, 'Active'),
    ('AAD', 'PROJECT_DELTA', tech_an800_id, 2, 'Active'),
    ('AAE', 'PROJECT_EPSILON', tech_an350_id, 1, 'Active'),
    ('AAF', 'PROJECT_ZETA', tech_an800_id, 1, 'Completed'),
    ('AAG', 'PROJECT_ETA', tech_an350_id, 3, 'Active'),
    ('AAH', 'PROJECT_THETA', tech_an200_id, 1, 'Active');
END $$;

-- =====================================================
-- VIEWS FOR EASY DATA ACCESS
-- =====================================================

-- Project summary view
CREATE VIEW project_summary AS
SELECT 
    p.id,
    p.name,
    p.asana_gid,
    t.name as technology,
    c.name as corridor,
    u.name as requester,
    u.acronym as requester_acronym,
    p.status,
    p.start_date,
    p.due_date,
    p.cycle_time_days,
    p.wafer_number,
    p.priority,
    p.created_at,
    p.updated_at
FROM projects p
LEFT JOIN technologies t ON p.technology_id = t.id
LEFT JOIN corridors c ON p.corridor_id = c.id
LEFT JOIN users u ON p.requester_id = u.id
WHERE p.is_active = true;

-- Task summary view
CREATE VIEW task_summary AS
SELECT 
    t.id,
    t.name,
    t.asana_gid,
    p.name as project_name,
    tt.name as task_type,
    t.start_date,
    t.due_date,
    t.status,
    t.completed,
    t.completed_at,
    t.created_at
FROM tasks t
LEFT JOIN projects p ON t.project_id = p.id
LEFT JOIN task_types tt ON t.task_type_id = tt.id
WHERE p.is_active = true;

-- Lot summary view
CREATE VIEW lot_summary AS
SELECT 
    l.id,
    l.lot_number,
    p.name as project_name,
    t.name as technology,
    c.name as corridor,
    u.name as requester,
    l.mask_code,
    l.run_number,
    l.wafer_count,
    l.status,
    l.reserved_date,
    l.confirmed_date,
    l.created_at
FROM lots l
LEFT JOIN projects p ON l.project_id = p.id
LEFT JOIN technologies t ON l.technology_id = t.id
LEFT JOIN corridors c ON l.corridor_id = c.id
LEFT JOIN users u ON l.requester_id = u.id;

-- Capacity overview view
CREATE VIEW capacity_overview AS
SELECT 
    c.name as corridor,
    t.name as technology,
    cc.max_lots,
    cc.max_wafers,
    cc.production_days,
    cc.task_delay_max_days,
    cc.task_advance_max_days,
    cc.max_reservation_days,
    COUNT(l.id) as current_lots,
    SUM(l.wafer_count) as current_wafers
FROM capacity_config cc
LEFT JOIN corridors c ON cc.corridor_id = c.id
LEFT JOIN technologies t ON cc.technology_id = t.id
LEFT JOIN lots l ON l.corridor_id = c.id AND l.technology_id = t.id AND l.status IN ('Reserved', 'Confirmed', 'In_Progress')
WHERE cc.is_active = true
GROUP BY c.name, t.name, cc.max_lots, cc.max_wafers, cc.production_days, cc.task_delay_max_days, cc.task_advance_max_days, cc.max_reservation_days
ORDER BY c.name, t.name;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE projects IS 'Main projects table storing all project information';
COMMENT ON TABLE tasks IS 'Individual tasks within projects, linked to Asana';
COMMENT ON TABLE lots IS 'Manufacturing lots associated with projects';
COMMENT ON TABLE masks IS 'Mask designs used in manufacturing';
COMMENT ON TABLE capacity_config IS 'Capacity configuration per technology and corridor';
COMMENT ON TABLE project_cache IS 'Performance cache for frequently accessed project data';
COMMENT ON TABLE audit_logs IS 'Audit trail for all data changes';

COMMENT ON COLUMN projects.asana_gid IS 'Asana project GID for integration';
COMMENT ON COLUMN tasks.custom_fields IS 'JSON storage for Asana custom field data';
COMMENT ON COLUMN project_cache.cached_data IS 'JSON cache of computed project data';
COMMENT ON COLUMN audit_logs.old_values IS 'JSON snapshot of data before change';
COMMENT ON COLUMN audit_logs.new_values IS 'JSON snapshot of data after change';
